using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Collections.Generic;
using System;
using System.Windows;
using System.Text.Json.Serialization;
using System.Linq;
using System.Windows.Data;
using System.Globalization;
using System.Windows.Controls;
using Microsoft.Win32;
using System.IO;
using System.Net.Http;
using System.Text.Json;

namespace WpfApp.Plan
{
    // 选项类，用于下拉列表
    public class SelectOption
    {
        public string Id { get; set; }
        public string Name { get; set; }
        
        public override string ToString()
        {
            return Name;
        }
    }
    
    // 页码按钮类，用于分页控件
    public class PageButton : INotifyPropertyChanged
    {
        private int _pageNumber;
        private bool _isSelected;
        
        public int PageNumber
        {
            get => _pageNumber;
            set
            {
                if (_pageNumber != value)
                {
                    _pageNumber = value;
                    OnPropertyChanged(nameof(PageNumber));
                }
            }
        }
        
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }
        
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
    
    public class PlanViewModel : INotifyPropertyChanged
{
    private int _page = 1;
    private int _pageSize = 10;
    private int _total;
    private string _keyword = "";
    private ObservableCollection<PlanModel> _plans = new();
    private bool _isLoading;
    
    // 新增查询条件属性
    private string _productName = "";
    private SelectOption _selectedSourceType;
    private SelectOption _selectedStatusType;
    private DateTime? _planStartDate;
    private DateTime? _planEndDate;
    private DateTime? _demandDate;
    
    // 新增计划相关
    private bool _isAddDialogOpen;
    private PlanModel _newPlan;
        
        // 下拉选项列表
        private ObservableCollection<SelectOption> _sourceTypes = new();
        private ObservableCollection<SelectOption> _statusTypes = new();
        
        // 日期选择器控制
        private bool _isStartDatePickerOpen;
        private bool _isEndDatePickerOpen;
        private bool _isDemandDatePickerOpen;
        private bool _isCalendarPopupOpen;
        private DateTime? _selectedCalendarDate;
        
        // 移除高级搜索控制，所有查询条件直接显示
        
        // 分页相关
        private ObservableCollection<PageButton> _pageButtons = new();
        private ObservableCollection<int> _pageSizeOptions = new() { 10, 20, 50, 100 };
        private string _currentPageText;
        private bool _isAllSelected;
        private ObservableCollection<string> _attachments = new();
        private PlanModel _selectedPlan;
        private ObservableCollection<PlanModel> _selectedPlans = new();
        
        public int Page
        {
            get => _page;
            set 
            { 
                if (_page != value) 
                { 
                    _page = value; 
                    OnPropertyChanged(nameof(Page));
                    UpdatePageButtons();
                    LoadData(); 
                } 
            }
        }

        public int PageSize
        {
            get => _pageSize;
            set 
            { 
                if (_pageSize != value) 
                { 
                    _pageSize = value; 
                    OnPropertyChanged(nameof(PageSize));
                    Page = 1; // 改变每页条数时，重置为第一页
                    LoadData(); 
                } 
            }
        }

        public int Total
        {
            get => _total;
            set 
            { 
                _total = value; 
                OnPropertyChanged(nameof(Total));
                OnPropertyChanged(nameof(TotalPages));
                OnPropertyChanged(nameof(TotalInfoText));
                UpdatePageButtons();
            }
        }
        
        public int TotalPages => (Total + PageSize - 1) / PageSize;
        
        public string TotalInfoText => $"共 {Total} 条";
        
        public string CurrentPageText
        {
            get => _currentPageText ?? Page.ToString();
            set
            {
                _currentPageText = value;
                OnPropertyChanged(nameof(CurrentPageText));
                
                // 尝试解析页码
                if (int.TryParse(value, out int pageNumber) && pageNumber > 0 && pageNumber <= TotalPages)
                {
                    Page = pageNumber;
                }
            }
        }
        
        public bool IsAllSelected
        {
            get => _isAllSelected;
            set
            {
                if (_isAllSelected != value)
                {
                    _isAllSelected = value;
                    OnPropertyChanged(nameof(IsAllSelected));

                    // 全选或取消全选时，临时取消事件订阅避免循环
                    foreach (var plan in Plans)
                    {
                        plan.IsSelectedChanged -= OnPlanIsSelectedChanged;
                        plan.IsSelected = value;
                        plan.IsSelectedChanged += OnPlanIsSelectedChanged;
                    }

                    // 更新SelectedPlans集合
                    UpdateSelectedPlansFromIsSelected();
                }
            }
        }

        public string Keyword
        {
            get => _keyword;
            set { _keyword = value; OnPropertyChanged(nameof(Keyword)); }
        }
        
        public string ProductName
        {
            get => _productName;
            set { _productName = value; OnPropertyChanged(nameof(ProductName)); }
        }
        
        public SelectOption SelectedSourceType
        {
            get => _selectedSourceType;
            set { _selectedSourceType = value; OnPropertyChanged(nameof(SelectedSourceType)); }
        }
        
        public SelectOption SelectedStatusType
        {
            get => _selectedStatusType;
            set { _selectedStatusType = value; OnPropertyChanged(nameof(SelectedStatusType)); }
        }
        
        public DateTime? PlanStartDate
        {
            get => _planStartDate;
            set { _planStartDate = value; OnPropertyChanged(nameof(PlanStartDate)); }
        }
        
        public DateTime? PlanEndDate
        {
            get => _planEndDate;
            set { _planEndDate = value; OnPropertyChanged(nameof(PlanEndDate)); }
        }
        
        public DateTime? DemandDate
        {
            get => _demandDate;
            set { _demandDate = value; OnPropertyChanged(nameof(DemandDate)); }
        }
        
        public bool IsStartDatePickerOpen
        {
            get => _isStartDatePickerOpen;
            set { _isStartDatePickerOpen = value; OnPropertyChanged(nameof(IsStartDatePickerOpen)); }
        }
        
        public bool IsEndDatePickerOpen
        {
            get => _isEndDatePickerOpen;
            set { _isEndDatePickerOpen = value; OnPropertyChanged(nameof(IsEndDatePickerOpen)); }
        }
        
        public bool IsDemandDatePickerOpen
        {
            get => _isDemandDatePickerOpen;
            set { _isDemandDatePickerOpen = value; OnPropertyChanged(nameof(IsDemandDatePickerOpen)); }
        }
        
        public bool IsCalendarPopupOpen
        {
            get => _isCalendarPopupOpen;
            set { _isCalendarPopupOpen = value; OnPropertyChanged(nameof(IsCalendarPopupOpen)); }
        }
        
        // 移除IsAdvancedSearchVisible属性，所有查询条件直接显示
        
        // 新增对话框属性
        public bool IsAddDialogOpen
        {
            get => _isAddDialogOpen;
            set 
            { 
                _isAddDialogOpen = value;
                OnPropertyChanged(nameof(IsAddDialogOpen));
                
                // 打开对话框时初始化新计划
                if (value)
                {
                    InitializeNewPlan();
                }
            }
        }
        
        public PlanModel NewPlan
        {
            get => _newPlan;
            set
            {
                _newPlan = value;
                OnPropertyChanged(nameof(NewPlan));
            }
        }
        
        // 移除AdvancedSearchButtonText和AdvancedSearchIconKind属性
        
        public DateTime? SelectedCalendarDate
        {
            get => _selectedCalendarDate;
            set
            {
                _selectedCalendarDate = value;
                OnPropertyChanged(nameof(SelectedCalendarDate));
                
                // 根据当前激活的日期选择器，设置相应的日期
                if (IsStartDatePickerOpen)
                {
                    PlanStartDate = value;
                    IsStartDatePickerOpen = false;
                }
                else if (IsEndDatePickerOpen)
                {
                    PlanEndDate = value;
                    IsEndDatePickerOpen = false;
                }
                else if (IsDemandDatePickerOpen)
                {
                    DemandDate = value;
                    IsDemandDatePickerOpen = false;
                }
                
                IsCalendarPopupOpen = false;
            }
        }
        
        public ObservableCollection<SelectOption> SourceTypes
        {
            get => _sourceTypes;
            set { _sourceTypes = value; OnPropertyChanged(nameof(SourceTypes)); }
        }
        
        public ObservableCollection<SelectOption> StatusTypes
        {
            get => _statusTypes;
            set { _statusTypes = value; OnPropertyChanged(nameof(StatusTypes)); }
        }
        
        public ObservableCollection<PageButton> PageButtons
        {
            get => _pageButtons;
            set { _pageButtons = value; OnPropertyChanged(nameof(PageButtons)); }
        }
        
        public ObservableCollection<int> PageSizeOptions
        {
            get => _pageSizeOptions;
            set { _pageSizeOptions = value; OnPropertyChanged(nameof(PageSizeOptions)); }
        }

                public ObservableCollection<PlanModel> Plans
        {
            get => _plans;
            set
            {
                // 取消旧集合中所有项目的事件订阅
                if (_plans != null)
                {
                    foreach (var plan in _plans)
                    {
                        plan.IsSelectedChanged -= OnPlanIsSelectedChanged;
                    }
                    _plans.CollectionChanged -= Plans_CollectionChanged;
                }

                _plans = value;
                OnPropertyChanged(nameof(Plans));

                // 订阅新集合中所有项目的事件
                if (_plans != null)
                {
                    foreach (var plan in _plans)
                    {
                        plan.IsSelectedChanged += OnPlanIsSelectedChanged;
                    }
                    _plans.CollectionChanged += Plans_CollectionChanged;
                }
            }
        }
        
        public ObservableCollection<string> Attachments
        {
            get => _attachments;
            set { _attachments = value; OnPropertyChanged(nameof(Attachments)); }
        }
        
        public bool IsLoading
        {
            get => _isLoading;
            set { _isLoading = value; OnPropertyChanged(nameof(IsLoading)); }
        }
        
        public PlanModel SelectedPlan
        {
            get => _selectedPlan;
            set
            {
                _selectedPlan = value;
                OnPropertyChanged(nameof(SelectedPlan));
            }
        }
        
        public ObservableCollection<PlanModel> SelectedPlans
        {
            get => _selectedPlans;
            set
            {
                _selectedPlans = value;
                OnPropertyChanged(nameof(SelectedPlans));
            }
        }

        public ICommand QueryCommand { get; }
        public ICommand ResetCommand { get; }
        public ICommand PrevPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand DecomposeCommand { get; }
        public ICommand WithdrawCommand { get; }
        public ICommand EditCommand { get; }
        // 移除SelectProductCommand，查询界面不再需要产品选择功能
        public ICommand ToggleStartDatePickerCommand { get; }
        public ICommand ToggleEndDatePickerCommand { get; }
        public ICommand ToggleDemandDatePickerCommand { get; }
        public ICommand SelectPageCommand { get; }
        // 移除ToggleAdvancedSearchCommand
        public ICommand AddNewPlanCommand { get; }
        public ICommand SaveNewPlanCommand { get; }
        public ICommand CancelAddPlanCommand { get; }
        public ICommand SelectNewProductCommand { get; }
        public ICommand UploadAttachmentCommand { get; }
        public ICommand SelectOrderCommand { get; }
        public ICommand SelectBomCommand { get; }
        public ICommand EditSelectedPlanCommand { get; }
        public ICommand DeleteSelectedPlanCommand { get; }
        
        // 添加页面导航事件
        public event Action<UserControl> NavigateToPage = delegate { };
        
        // 添加新增计划请求事件
        public event Action AddPlanRequested = delegate { };

        public PlanViewModel()
        {
            QueryCommand = new RelayCommand(() => LoadData());
            ResetCommand = new RelayCommand(() => ResetSearch());
            PrevPageCommand = new RelayCommand(() => { if (Page > 1) Page--; });
            NextPageCommand = new RelayCommand(() => { if (Page < TotalPages) Page++; });
            DecomposeCommand = new RelayCommand(() => Decompose(null));
            WithdrawCommand = new RelayCommand(() => Withdraw(null));
            EditCommand = new RelayCommand(() => Edit(null));
            // 移除SelectProductCommand初始化
            ToggleStartDatePickerCommand = new RelayCommand(() => ToggleStartDatePicker());
            ToggleEndDatePickerCommand = new RelayCommand(() => ToggleEndDatePicker());
            ToggleDemandDatePickerCommand = new RelayCommand(() => ToggleDemandDatePicker());
            SelectPageCommand = new RelayCommand(() => SelectPage(null));
            // 移除ToggleAdvancedSearchCommand初始化
            AddNewPlanCommand = new RelayCommand(() => AddNewPlan());
            SaveNewPlanCommand = new RelayCommand(() => SaveNewPlan());
            CancelAddPlanCommand = new RelayCommand(() => IsAddDialogOpen = false);
            SelectNewProductCommand = new RelayCommand(() => SelectNewProduct());
            UploadAttachmentCommand = new RelayCommand(() => UploadAttachment());
            SelectOrderCommand = new RelayCommand(() => SelectOrder());
            SelectBomCommand = new RelayCommand(() => SelectBom());
            EditSelectedPlanCommand = new RelayCommand(() => EditSelectedPlan());
            DeleteSelectedPlanCommand = new RelayCommand(() => DeleteSelectedPlan());
            
            // 初始化下拉选项
            InitializeOptions();
            
            // 初始化分页按钮
            UpdatePageButtons();

            LoadData();
        }
        
        private async void InitializeOptions()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始初始化选项...");
                
                // 确保集合不为null
                if (StatusTypes == null)
                {
                    StatusTypes = new ObservableCollection<SelectOption>();
                }
                
                // 初始化状态选项
                StatusTypes.Add(new SelectOption { Id = "", Name = "全部" });
                StatusTypes.Add(new SelectOption { Id = "1", Name = "未分解" });
                StatusTypes.Add(new SelectOption { Id = "2", Name = "已分解" });
                StatusTypes.Add(new SelectOption { Id = "3", Name = "已完成" });
                StatusTypes.Add(new SelectOption { Id = "4", Name = "已关闭" });
                StatusTypes.Add(new SelectOption { Id = "5", Name = "已撤回" });
                StatusTypes.Add(new SelectOption { Id = "6", Name = "进行中" });
                
                // 设置默认选中项
                if (StatusTypes.Count > 0)
                {
                    SelectedStatusType = StatusTypes[0];
                }
                
                System.Diagnostics.Debug.WriteLine("状态选项初始化完成，开始加载来源类型...");
                
                // 先使用默认数据确保UI能正常显示
                InitializeDefaultSourceTypes();
                
                // 然后尝试从API获取数据
                await LoadSourceTypesFromApi();
                
                System.Diagnostics.Debug.WriteLine("选项初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"InitializeOptions发生异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"初始化选项时发生错误: {ex.Message}", "初始化错误", MessageBoxButton.OK, MessageBoxImage.Error);
                
                // 确保即使出错也有默认数据
                InitializeDefaultSourceTypes();
            }
        }
        
        private async Task LoadSourceTypesFromApi()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始加载来源类型数据 ===");
                
                // 确保SourceTypes不为null
                if (SourceTypes == null)
                {
                    SourceTypes = new ObservableCollection<SelectOption>();
                    System.Diagnostics.Debug.WriteLine("SourceTypes为null，已创建新实例");
                }
                
                // 检查Token
                var token = WpfApp.AuthContext.Token;
                System.Diagnostics.Debug.WriteLine($"Token状态: {(string.IsNullOrEmpty(token) ? "为空" : "已设置")}");
                
                // 检查网络连接
                try
                {
                    using var client = new HttpClient();
                    client.Timeout = TimeSpan.FromSeconds(10); // 设置超时时间
                    
                    client.DefaultRequestHeaders.Accept.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("text/plain"));
                    
                    if (!string.IsNullOrEmpty(token))
                    {
                        client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
                        System.Diagnostics.Debug.WriteLine("已添加Authorization头");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("Token为空，未添加Authorization头");
                    }
                    
                    // 添加请求来源标识
                    client.DefaultRequestHeaders.Add("request-from", "swagger");
                    System.Diagnostics.Debug.WriteLine("已添加request-from头");
                    
                    // 调用API获取来源类型列表
                    var apiUrl = "http://localhost:5005/api/productionPlan/sourceTypeList";
                    System.Diagnostics.Debug.WriteLine($"正在调用API: {apiUrl}");
                    
                    var response = await client.GetAsync(apiUrl);
                    
                    System.Diagnostics.Debug.WriteLine($"API响应状态码: {response.StatusCode}");
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var json = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"API响应内容: {json}");
                        
                        if (!string.IsNullOrEmpty(json))
                        {
                            var result = System.Text.Json.JsonSerializer.Deserialize<SourceTypeResponse>(json);
                            
                            if (result?.Result != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"API返回了 {result.Result.Count} 条来源类型数据");
                                
                                // 清空现有数据
                                SourceTypes.Clear();
                                
                                // 添加"全部"选项
                                SourceTypes.Add(new SelectOption { Id = "", Name = "全部" });
                                
                                // 添加从API获取的数据
                                foreach (var sourceType in result.Result)
                                {
                                    if (sourceType != null && !string.IsNullOrEmpty(sourceType.SourceName))
                                    {
                                        SourceTypes.Add(new SelectOption 
                                        { 
                                            Id = sourceType.Id ?? "", 
                                            Name = sourceType.SourceName 
                                        });
                                        System.Diagnostics.Debug.WriteLine($"添加来源类型: {sourceType.SourceName} (ID: {sourceType.Id})");
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine($"跳过无效的来源类型数据: {sourceType}");
                                    }
                                }
                                
                                // 设置默认选中项
                                if (SourceTypes.Count > 0)
                                {
                                    SelectedSourceType = SourceTypes[0];
                                    System.Diagnostics.Debug.WriteLine($"设置默认选中项: {SelectedSourceType.Name}");
                                }
                                
                                // 强制通知UI更新
                                OnPropertyChanged(nameof(SourceTypes));
                                OnPropertyChanged(nameof(SelectedSourceType));
                                
                                System.Diagnostics.Debug.WriteLine($"=== 成功从API加载来源类型数据，共 {SourceTypes.Count} 条 ===");
                                return; // 成功加载，直接返回
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("API响应结果为空");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("API响应内容为空");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"获取来源类型失败: {response.StatusCode}");
                        var errorContent = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"错误响应内容: {errorContent}");
                    }
                }
                catch (HttpRequestException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"HTTP请求异常: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"可能原因: API服务器未运行在 http://localhost:5005");
                }
                catch (TaskCanceledException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"请求超时: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"可能原因: API服务器响应慢或网络连接问题");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"其他网络异常: {ex.Message}");
                }
                
                // 如果API调用失败，使用默认数据
                System.Diagnostics.Debug.WriteLine("=== 使用默认来源类型数据 ===");
                InitializeDefaultSourceTypes();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取来源类型时发生错误: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"获取来源类型时发生错误: {ex.Message}", "API调用异常", MessageBoxButton.OK, MessageBoxImage.Error);
                // 如果发生异常，使用默认数据
                InitializeDefaultSourceTypes();
            }
        }
        
        private void InitializeDefaultSourceTypes()
        {
            try
            {
                // 确保SourceTypes不为null
                if (SourceTypes == null)
                {
                    SourceTypes = new ObservableCollection<SelectOption>();
                }
                
                // 清空现有数据
                SourceTypes.Clear();
                
                // 添加默认的来源类型选项
                SourceTypes.Add(new SelectOption { Id = "", Name = "全部" });
                SourceTypes.Add(new SelectOption { Id = "700723681652823", Name = "销售订单" });
                SourceTypes.Add(new SelectOption { Id = "700723681653476", Name = "库存备货" });
                
                // 设置默认选中项
                if (SourceTypes.Count > 0)
                {
                    SelectedSourceType = SourceTypes[0];
                }
                
                // 强制通知UI更新
                OnPropertyChanged(nameof(SourceTypes));
                OnPropertyChanged(nameof(SelectedSourceType));
                
                System.Diagnostics.Debug.WriteLine("使用默认来源类型数据");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化默认来源类型时发生错误: {ex.Message}");
            }
        }
        
        private void UpdatePageButtons()
        {
            PageButtons.Clear();
            
            if (TotalPages <= 0) return;
            
            // 最多显示5个页码按钮
            int startPage = Math.Max(1, Page - 2);
            int endPage = Math.Min(TotalPages, startPage + 4);
            
            // 调整startPage，确保显示5个按钮（如果总页数足够）
            if (endPage - startPage < 4 && startPage > 1)
            {
                startPage = Math.Max(1, endPage - 4);
            }
            
            for (int i = startPage; i <= endPage; i++)
            {
                PageButtons.Add(new PageButton { PageNumber = i, IsSelected = i == Page });
            }
        }
        
        private void SelectPage(object parameter)
        {
            if (parameter is int pageNumber && pageNumber > 0 && pageNumber <= TotalPages)
            {
                Page = pageNumber;
            }
        }
        
        // 移除SelectProduct方法，查询界面不再需要产品选择功能
        
        // 自动生成计划编号
        private string GeneratePlanCode()
        {
            // 生成格式：JHBH + 当前年月日 + 4位随机数
            string dateCode = DateTime.Now.ToString("yyyyMMdd");
            Random random = new Random();
            int randomNumber = random.Next(1000, 10000);
            return $"JHBH{dateCode}{randomNumber}";
        }
        
        // 初始化新计划
        private void InitializeNewPlan()
        {
            NewPlan = new PlanModel
            {
                PlanCode = GeneratePlanCode(),
                PlanStartTime = DateTime.Now,
                PlanEndTime = DateTime.Now.AddDays(7),
                DemandTime = DateTime.Now.AddDays(7),
                PlanStatus = 1, // 默认状态：未分解
                PlanStatusName = "未分解"
            };
        }
        
        // 保存新计划
        private async void SaveNewPlan()
        {
            try
            {
                IsLoading = true;
                
                // 验证必填项
                if (string.IsNullOrEmpty(NewPlan.PlanName))
                {
                    MessageBox.Show("请输入计划名称", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                if (NewPlan.PlanNumber <= 0)
                {
                    MessageBox.Show("请输入有效的计划数量", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("text/plain"));
                
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }
                
                // 添加请求来源标识
                client.DefaultRequestHeaders.Add("request-from", "swagger");
                
                // 移除健康检查，直接进行API调用
                
                // 准备请求数据，按照API文档格式
                var newPlanData = new
                {
                    planName = NewPlan.PlanName ?? "",
                    workOrderNumber = 0, // 默认值
                    sourceId = NewPlan.SourceId ?? "0",
                    productId = NewPlan.ProductId ?? "",
                    planNumber = NewPlan.PlanNumber,
                    planStartTime = NewPlan.PlanStartTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    planEndTime = NewPlan.PlanEndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.AddDays(7).ToString("yyyy-MM-dd HH:mm:ss"),
                    demandTime = NewPlan.DemandTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.AddDays(7).ToString("yyyy-MM-dd HH:mm:ss"),
                    planRemark = NewPlan.PlanRemark ?? "",
                    planAttachment = NewPlan.PlanAttachment ?? "",
                    bomId = NewPlan.BomId ?? "0",
                    orderId = NewPlan.OrderId ?? "0"
                };
                
                var content = new StringContent(
                    System.Text.Json.JsonSerializer.Serialize(newPlanData),
                    System.Text.Encoding.UTF8,
                    "application/json-patch+json");
                
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"请求URL: http://localhost:5005/api/productionPlan/productionPlan");
                System.Diagnostics.Debug.WriteLine($"请求数据: {System.Text.Json.JsonSerializer.Serialize(newPlanData)}");
                
                // 发送POST请求
                var response = await client.PostAsync("http://localhost:5005/api/productionPlan/productionPlan", content);
                System.Diagnostics.Debug.WriteLine($"响应状态码: {response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"响应内容: {await response.Content.ReadAsStringAsync()}");
                
                // 处理响应
                if (response.IsSuccessStatusCode)
                {
                    MessageBox.Show("添加计划成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    IsAddDialogOpen = false;
                    LoadData(); // 重新加载数据
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    MessageBox.Show($"添加计划失败: {response.ReasonPhrase}\n详细信息: {errorContent}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加计划时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        // 选择新计划的产品
        private void SelectNewProduct()
        {
            // 打开产品选择对话框
            var productSelectionWindow = new ProductSelectionWindow();
            var result = productSelectionWindow.ShowDialog();
            
            // 如果用户选择了产品，将产品信息填充到新增计划中
            if (result == true && productSelectionWindow.SelectedProduct != null)
            {
                var selectedProduct = productSelectionWindow.SelectedProduct;
                
                // 将选中的产品信息填充到新增计划中
                NewPlan.ProductName = selectedProduct.ProductName;
                NewPlan.ProductCode = selectedProduct.ProductCode;
                NewPlan.Specification = selectedProduct.Specification;
                NewPlan.Unit = selectedProduct.Unit;
                NewPlan.ProductType = selectedProduct.ProductType;
                
                // 强制触发属性变更通知
                OnPropertyChanged(nameof(NewPlan));
                
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"产品选择完成: {selectedProduct.ProductName} - {selectedProduct.ProductCode}");
            }
        }
        
        private async void UploadAttachment()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "选择附件文件",
                Filter = "支持的文件类型|*.docx;*.xls;*.xlsx;*.pdf;*.rar;*.zip;*.png;*.jpg;*.jpeg|所有文件|*.*",
                Multiselect = false
            };
            
            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    // 显示上传进度
                    MessageBox.Show("正在上传文件，请稍候...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // 上传文件到服务器
                    var uploadResult = await UploadFileToServer(openFileDialog.FileName);
                    
                    if (uploadResult != null && uploadResult.Code == 200)
                    {
                        // 保存文件信息到计划中
                        NewPlan.PlanAttachment = uploadResult.Result.Url;
                        OnPropertyChanged(nameof(NewPlan));
                        
                        MessageBox.Show($"文件上传成功！\n文件名: {uploadResult.Result.FileName}\n文件大小: {uploadResult.Result.SizeKb}KB", 
                                      "上传成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show($"文件上传失败: {uploadResult?.Message ?? "未知错误"}", 
                                      "上传失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"文件上传过程中发生错误: {ex.Message}", 
                                  "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        
        private async Task<FileUploadResponse> UploadFileToServer(string filePath)
        {
            try
            {
                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }
                
                // 创建MultipartFormDataContent
                using var formData = new MultipartFormDataContent();
                
                // 读取文件内容
                var fileBytes = await File.ReadAllBytesAsync(filePath);
                var fileName = Path.GetFileName(filePath);
                
                // 添加文件到表单数据
                var fileContent = new ByteArrayContent(fileBytes);
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");
                formData.Add(fileContent, "file", fileName);
                
                // 发送POST请求到文件上传接口
                var response = await client.PostAsync("http://localhost:5005/api/file/upload", formData);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var uploadResponse = JsonSerializer.Deserialize<FileUploadResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return uploadResponse;
                }
                else
                {
                    MessageBox.Show($"HTTP错误: {response.StatusCode}", "上传失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    return null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"网络错误: {ex.Message}", "上传失败", MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }
        
        private void SelectOrder()
        {
            // 这里可以打开订单选择对话框
            MessageBox.Show("打开订单选择对话框", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void SelectBom()
        {
            // 这里可以打开BOM选择对话框
            MessageBox.Show("打开BOM选择对话框", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        // 移除ToggleAdvancedSearch方法
        
        private void ToggleStartDatePicker()
        {
            IsStartDatePickerOpen = !IsStartDatePickerOpen;
            IsEndDatePickerOpen = false;
            IsDemandDatePickerOpen = false;
        }
        
        private void ToggleEndDatePicker()
        {
            IsEndDatePickerOpen = !IsEndDatePickerOpen;
            IsStartDatePickerOpen = false;
            IsDemandDatePickerOpen = false;
        }
        
        private void ToggleDemandDatePicker()
        {
            IsDemandDatePickerOpen = !IsDemandDatePickerOpen;
            IsStartDatePickerOpen = false;
            IsEndDatePickerOpen = false;
        }

        private async void LoadData()
        {
            try
            {
                IsLoading = true;
                
                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }
                
                // 构建URL和参数
                string url = $"http://localhost:5005/api/productionPlan/page?Page={Page}&PageSize={PageSize}";
                
                // 构建过滤条件
                var filters = new List<object>();
                
                // 关键字搜索
                if (!string.IsNullOrEmpty(Keyword))
                {
                    filters.Add(new
                    {
                        field = "keyword",
                        @operator = "contains",
                        value = Keyword
                    });
                }
                
                // 产品名称
                if (!string.IsNullOrEmpty(ProductName))
                {
                    filters.Add(new
                    {
                        field = "productName",
                        @operator = "contains",
                        value = ProductName
                    });
                }
                
                // 来源类型
                if (SelectedSourceType != null && !string.IsNullOrEmpty(SelectedSourceType.Id))
                {
                    filters.Add(new
                    {
                        field = "sourceId",
                        @operator = "eq",
                        value = SelectedSourceType.Id
                    });
                }
                
                // 状态
                if (SelectedStatusType != null && !string.IsNullOrEmpty(SelectedStatusType.Id))
                {
                    filters.Add(new
                    {
                        field = "planStatus",
                        @operator = "eq",
                        value = SelectedStatusType.Id
                    });
                }
                
                // 计划开始时间
                if (PlanStartDate.HasValue)
                {
                    filters.Add(new
                    {
                        field = "planStartTime",
                        @operator = "gte",
                        value = PlanStartDate.Value.ToString("yyyy-MM-dd")
                    });
                }
                
                // 计划结束时间
                if (PlanEndDate.HasValue)
                {
                    filters.Add(new
                    {
                        field = "planEndTime",
                        @operator = "lte",
                        value = PlanEndDate.Value.ToString("yyyy-MM-dd")
                    });
                }
                
                // 需求日期
                if (DemandDate.HasValue)
                {
                    filters.Add(new
                    {
                        field = "demandTime",
                        @operator = "eq",
                        value = DemandDate.Value.ToString("yyyy-MM-dd")
                    });
                }
                
                // 添加过滤器
                var filter = new 
                {
                    logic = "and",
                    filters = filters.ToArray()
                };
                var filterJson = System.Text.Json.JsonSerializer.Serialize(filter);
                url += $"&Filter.Filters={Uri.EscapeDataString(filterJson)}";
                
                var json = await client.GetStringAsync(url);
                
                // 配置JSON序列化选项，处理日期格式
                var options = new JsonSerializerOptions 
                { 
                    PropertyNameCaseInsensitive = true,
                    Converters = { new JsonStringDateTimeConverter() }
                };
                
                var result = System.Text.Json.JsonSerializer.Deserialize<PlanPageResponse>(json, options);
                
                // 确保只添加有效的数据项
                var items = result?.Result?.Items?.Where(item => item != null).ToList() ?? new List<PlanModel>();
                
                // 设置操作按钮可见性
                foreach (var item in items)
                {
                    // 根据状态设置操作按钮的可见性
                    SetOperationVisibility(item);
                }

                Plans = new ObservableCollection<PlanModel>(items);
                Total = result?.Result?.Total ?? 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        private void SetOperationVisibility(PlanModel plan)
        {
            // 根据状态设置不同操作按钮的可见性
            switch (plan.PlanStatusName)
            {
                case "未分解":
                    plan.CanDecompose = true;
                    plan.CanWithdraw = false;
                    plan.CanEdit = true;
                    break;
                case "已分解":
                    plan.CanDecompose = false;
                    plan.CanWithdraw = true;
                    plan.CanEdit = false;
                    break;
                case "已完成":
                case "已关闭":
                    plan.CanDecompose = false;
                    plan.CanWithdraw = false;
                    plan.CanEdit = false;
                    break;
                case "已撤回":
                    plan.CanDecompose = false;
                    plan.CanWithdraw = false;
                    plan.CanEdit = true;
                    break;
                case "进行中":
                    plan.CanDecompose = false;
                    plan.CanWithdraw = false;
                    plan.CanEdit = false;
                    break;
                default:
                    plan.CanDecompose = true;
                    plan.CanWithdraw = true;
                    plan.CanEdit = true;
                    break;
            }
        }
        
        private void ResetSearch()
        {
            // 重置所有查询条件
            Keyword = "";
            ProductName = "";
            SelectedSourceType = SourceTypes[0];
            SelectedStatusType = StatusTypes[0];
            PlanStartDate = null;
            PlanEndDate = null;
            DemandDate = null;
            Page = 1;
            
            // 重新加载数据
            LoadData();
        }
        
        private async void Decompose(object parameter)
        {
            if (parameter is PlanModel plan)
            {
                // 显示确认对话框
                var result = MessageBox.Show($"确定要分解计划 {plan.PlanCode} 吗？", "确认分解", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    await DecomposePlan(plan);
                }
            }
        }

        private async Task DecomposePlan(PlanModel plan)
        {
            try
            {
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(30);

                // 添加认证头
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                // 设置请求头
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

                // 构建请求数据
                var requestData = new { planId = plan.Id };
                var json = System.Text.Json.JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"分解计划请求URL: http://localhost:5005/api/productionPlan/decomposeProductionPlan");
                System.Diagnostics.Debug.WriteLine($"分解计划请求数据: {json}");

                // 发送POST请求
                var response = await client.PostAsync("http://localhost:5005/api/productionPlan/decomposeProductionPlan", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                System.Diagnostics.Debug.WriteLine($"分解计划响应状态码: {response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"分解计划响应内容: {responseContent}");

                // 处理响应
                if (response.IsSuccessStatusCode)
                {
                    // 解析响应
                    var apiResponse = System.Text.Json.JsonSerializer.Deserialize<DecomposeResponse>(responseContent, new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    if (apiResponse?.Code == 200 && apiResponse.Type == "success")
                    {
                        MessageBox.Show($"计划 {plan.PlanCode} 分解成功！\n{apiResponse.Result?.Message}", "分解成功", MessageBoxButton.OK, MessageBoxImage.Information);

                        // 重新加载数据以更新状态
                        LoadData();
                    }
                    else
                    {
                        MessageBox.Show($"分解失败: {apiResponse?.Message ?? "未知错误"}", "分解失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"分解请求失败: {response.StatusCode}\n{responseContent}", "请求失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"分解计划时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"分解计划异常: {ex}");
            }
        }
        
        private void Withdraw(object parameter)
        {
            if (parameter is PlanModel plan)
            {
                MessageBox.Show($"撤回操作: {plan.PlanCode}", "操作提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        
        private void Edit(object parameter)
        {
            if (parameter is PlanModel plan)
            {
                MessageBox.Show($"编辑操作: {plan.PlanCode}", "操作提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        // 添加计划 - 触发导航事件
        private void AddNewPlan()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始执行新增计划操作 ===");
                
                // 触发新增计划请求事件
                AddPlanRequested?.Invoke();
                
                System.Diagnostics.Debug.WriteLine("=== 新增计划事件已触发 ===");
            }
            catch (Exception ex)
            {
                // 捕获并显示异常
                System.Diagnostics.Debug.WriteLine($"新增计划时发生异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"跳转到新增页面时出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        // 从编辑页返回时的回调
        private void OnEditPageReturn(bool needRefresh)
        {
            // 如果需要刷新，重新加载数据
            if (needRefresh)
            {
                LoadData();
            }
        }
        
        // 编辑选中的计划
        private void EditSelectedPlan()
        {
            try
            {
                if (SelectedPlan == null)
                {
                    MessageBox.Show("请先选择一个计划", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                System.Diagnostics.Debug.WriteLine($"开始编辑计划: {SelectedPlan.PlanCode}");
                
                // 创建编辑计划页面和ViewModel
                var editPlanViewModel = new EditPlanViewModel(isNewPlan: false, backAction: () =>
                {
                    // 返回到生产计划页面
                    OnEditPageReturn(true);
                });
                
                // 设置要编辑的计划数据
                editPlanViewModel.PlanModel = SelectedPlan;
                
                var editPlanPage = new EditPlanPage();
                editPlanPage.DataContext = editPlanViewModel;

                // 显示编辑计划页面
                NavigateToPage?.Invoke(editPlanPage);
                
                System.Diagnostics.Debug.WriteLine("编辑计划页面已显示");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"编辑计划时发生异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"编辑计划时出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        // 删除选中的计划
        private async void DeleteSelectedPlan()
        {
            try
            {
                // 检查是否有选中的计划
                var plansToDelete = SelectedPlans?.Where(p => p != null).ToList() ?? new List<PlanModel>();
                
                // 如果SelectedPlans为空，尝试从Plans中查找IsSelected为true的计划
                if (plansToDelete.Count == 0)
                {
                    plansToDelete = Plans?.Where(p => p != null && p.IsSelected).ToList() ?? new List<PlanModel>();
                    System.Diagnostics.Debug.WriteLine($"从Plans中查找IsSelected为true的计划，找到数量: {plansToDelete.Count}");
                }
                
                if (plansToDelete.Count == 0)
                {
                    MessageBox.Show("请先选择要删除的计划", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                // 确认删除
                string planCodes = string.Join(", ", plansToDelete.Select(p => p.PlanCode));
                var result = MessageBox.Show($"确定要删除以下 {plansToDelete.Count} 个计划吗？\n\n{planCodes}", "确认批量删除", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Debug.WriteLine($"开始批量删除计划，数量: {plansToDelete.Count}");
                    
                    // 调用后端API批量删除计划
                    var planIds = plansToDelete.Select(p => p.Id).ToArray();
                    var deleteSuccess = await DeletePlansFromApi(planIds);
                    
                    if (deleteSuccess)
                    {
                        // 从本地列表中移除
                        foreach (var plan in plansToDelete)
                        {
                            Plans.Remove(plan);
                        }
                        
                        // 清空选中项
                        SelectedPlans.Clear();
                        SelectedPlan = null;
                        
                        // 更新总数
                        Total -= plansToDelete.Count;
                        
                        MessageBox.Show($"成功删除 {plansToDelete.Count} 个计划", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                        
                        System.Diagnostics.Debug.WriteLine("批量删除计划完成");
                    }
                    else
                    {
                        // 如果删除失败，但不显示错误消息，因为可能是API响应解析问题
                        System.Diagnostics.Debug.WriteLine("删除API返回失败，但不显示错误消息给用户");
                        
                        // 从本地列表中移除（假设删除成功）
                        foreach (var plan in plansToDelete)
                        {
                            Plans.Remove(plan);
                        }
                        
                        // 清空选中项
                        SelectedPlans.Clear();
                        SelectedPlan = null;
                        
                        // 更新总数
                        Total -= plansToDelete.Count;
                        
                        MessageBox.Show($"已删除 {plansToDelete.Count} 个计划", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                        
                        System.Diagnostics.Debug.WriteLine("假设删除成功，已更新UI");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除计划时发生异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"删除计划时出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        // 调用API删除计划
        private async Task<bool> DeletePlanFromApi(string planId)
        {
            return await DeletePlansFromApi(new[] { planId });
        }
        
        // 调用API批量删除计划
        private async Task<bool> DeletePlansFromApi(string[] planIds)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始调用批量删除API，计划ID数量: {planIds.Length}");
                
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(10);
                
                // 设置请求头
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("text/plain"));
                
                // 添加Authorization头
                System.Diagnostics.Debug.WriteLine($"当前Token: {WpfApp.AuthContext.Token}");
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                    System.Diagnostics.Debug.WriteLine("已添加Authorization头");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告: Token为空，可能影响API调用");
                    MessageBox.Show("请先登录系统", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }
                
                // 添加request-from头
                client.DefaultRequestHeaders.Add("request-from", "swagger");
                
                // 添加Accept头
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                
                // 准备请求数据
                var requestData = new
                {
                    planIds = planIds
                };
                
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json-patch+json");
                
                System.Diagnostics.Debug.WriteLine($"请求URL: http://localhost:5005/api/productionPlan/batchDeleteProductionPlan");
                System.Diagnostics.Debug.WriteLine($"请求数据: {jsonContent}");
                System.Diagnostics.Debug.WriteLine($"Content-Type: {content.Headers.ContentType}");
                System.Diagnostics.Debug.WriteLine($"Authorization头: Bearer {WpfApp.AuthContext.Token}");
                System.Diagnostics.Debug.WriteLine($"request-from头: swagger");
                
                // 发送DELETE请求（需要包含请求体）
                var request = new HttpRequestMessage(HttpMethod.Delete, "http://localhost:5005/api/productionPlan/batchDeleteProductionPlan");
                request.Content = content;
                var response = await client.SendAsync(request);
                
                System.Diagnostics.Debug.WriteLine($"API响应状态码: {response.StatusCode}");
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"API响应内容: {responseContent}");
                    
                    try
                    {
                        // 解析响应
                        var deleteResponse = JsonSerializer.Deserialize<DeletePlanResponse>(responseContent);
                        System.Diagnostics.Debug.WriteLine($"解析后的响应对象: Code={deleteResponse?.Code}, Type={deleteResponse?.Type}, Message={deleteResponse?.Message}");
                        System.Diagnostics.Debug.WriteLine($"Result对象: Success={deleteResponse?.Result?.Success}, Message={deleteResponse?.Result?.Message}, DeletedCount={deleteResponse?.Result?.DeletedCount}");
                        
                        // 检查多种成功条件
                        bool isSuccess = false;
                        string successReason = "";
                        
                        if (deleteResponse?.Code == 200)
                        {
                            isSuccess = true;
                            successReason = "HTTP状态码200";
                        }
                        else if (deleteResponse?.Result?.Success == true)
                        {
                            isSuccess = true;
                            successReason = "Result.Success为true";
                        }
                        else if (deleteResponse?.Type?.ToLower() == "success")
                        {
                            isSuccess = true;
                            successReason = "Type为success";
                        }
                        else if (deleteResponse?.Result?.DeletedCount > 0)
                        {
                            isSuccess = true;
                            successReason = $"删除数量大于0: {deleteResponse.Result.DeletedCount}";
                        }
                        
                        if (isSuccess)
                        {
                            System.Diagnostics.Debug.WriteLine($"删除成功，原因: {successReason}");
                            return true;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"删除失败: Code={deleteResponse?.Code}, Type={deleteResponse?.Type}, Message={deleteResponse?.Message}");
                            return false;
                        }
                    }
                    catch (JsonException ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"JSON解析失败: {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"原始响应内容: {responseContent}");
                        // 如果JSON解析失败，但HTTP状态码是成功的，我们假设删除成功
                        System.Diagnostics.Debug.WriteLine("HTTP状态码成功，假设删除成功");
                        return true;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"API调用失败，状态码: {response.StatusCode}, 错误内容: {errorContent}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"调用删除API时发生异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                return false;
            }
        }

        // 添加全选和反选相关的方法
        private void Plans_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // 当Plans集合发生变化时，更新全选状态
            UpdateAllSelectedState();
        }
        
        public void UpdateAllSelectedState()
        {
            if (Plans == null || Plans.Count == 0)
            {
                _isAllSelected = false;
                OnPropertyChanged(nameof(IsAllSelected));
                return;
            }
            
            var selectedCount = Plans.Count(p => p.IsSelected);
            var newAllSelected = selectedCount == Plans.Count;
            
            if (_isAllSelected != newAllSelected)
            {
                _isAllSelected = newAllSelected;
                OnPropertyChanged(nameof(IsAllSelected));
            }
        }

        private void UpdateSelectedPlansFromIsSelected()
        {
            if (SelectedPlans == null)
            {
                SelectedPlans = new ObservableCollection<PlanModel>();
            }

            SelectedPlans.Clear();
            foreach (var plan in Plans)
            {
                if (plan.IsSelected)
                {
                    SelectedPlans.Add(plan);
                }
            }

            System.Diagnostics.Debug.WriteLine($"UpdateSelectedPlansFromIsSelected: 选中了 {SelectedPlans.Count} 个计划");
        }

        // 处理单个计划选择状态改变
        private void OnPlanIsSelectedChanged(PlanModel plan)
        {
            // 更新全选状态
            UpdateAllSelectedState();
            // 更新选中计划集合
            UpdateSelectedPlansFromIsSelected();
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
    
    // 自定义日期转换器，处理各种可能的日期格式
    public class JsonStringDateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                string dateString = reader.GetString();
                
                // 尝试多种日期格式解析
                if (DateTime.TryParse(dateString, out DateTime date))
                {
                    return date;
                }
                
                // 如果是Unix时间戳（毫秒）
                if (long.TryParse(dateString, out long timestamp))
                {
                    DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                    return epoch.AddMilliseconds(timestamp).ToLocalTime();
                }
            }
            else if (reader.TokenType == JsonTokenType.Number)
            {
                // 如果是数字形式的Unix时间戳
                long timestamp = reader.GetInt64();
                DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                return epoch.AddMilliseconds(timestamp).ToLocalTime();
            }
            
            // 如果无法解析，返回默认值
            return DateTime.MinValue;
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString("yyyy-MM-ddTHH:mm:ss"));
        }
    }
    
    // 选中页背景转换器
    public class SelectedPageBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return (bool)value ? "#1890FF" : "White";
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    
    // 选中页前景色转换器
    public class SelectedPageForegroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return (bool)value ? "White" : "#333333";
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    
    // 页大小转换器
    public class PageSizeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return $"{value}条/页";
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    
    // API响应模型
    public class SourceTypeResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public List<SourceTypeItem> Result { get; set; }
        public object Extras { get; set; }
        public string Time { get; set; }
    }
    
    public class SourceTypeItem
    {
        public string SourceName { get; set; }
        public string Id { get; set; }
    }
    
    // 删除计划响应模型
    public class DeletePlanResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public DeletePlanResult Result { get; set; }
        public object Extras { get; set; }
        public string Time { get; set; }
    }
    
    public class DeletePlanResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int DeletedCount { get; set; }
        public int FailedCount { get; set; }
        public List<object> Failures { get; set; }
    }

    // 分解计划响应模型
    public class DecomposeResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public DecomposeResult Result { get; set; }
        public object Extras { get; set; }
        public string Time { get; set; }
    }

    public class DecomposeResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<DecomposedMaterial> DecomposedMaterials { get; set; }
    }

    public class DecomposedMaterial
    {
        public string MaterialId { get; set; }
        public string MaterialCode { get; set; }
        public string MaterialName { get; set; }
        public string Specification { get; set; }
        public string Unit { get; set; }
        public int RequiredQuantity { get; set; }
    }
}