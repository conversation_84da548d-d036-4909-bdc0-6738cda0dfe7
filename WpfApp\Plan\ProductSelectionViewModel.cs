using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Threading;

namespace WpfApp.Plan
{
    public class ProductSelectionViewModel : INotifyPropertyChanged
    {
        private readonly HttpClient _httpClient;
        private CancellationTokenSource _searchCancellationTokenSource;

        private ObservableCollection<ProductDisplayModel> _products = new ObservableCollection<ProductDisplayModel>();
        private bool _isLoading;
        private string _searchKeyword = "";
        private ProductDisplayModel _selectedProduct;
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalCount;
        private int _totalPages;
        private int _goToPage;
        private string _errorMessage = "";

        // 搜索条件
        private string _selectedUnit = "";
        private string _selectedProductType = "";
        private string _selectedProductAttribute = "";

        // 选项列表
        private List<string> _unitOptions = new List<string> { "", "个", "kg", "套", "台", "箱", "吨", "升", "件" };
        private List<string> _productTypeOptions = new List<string> { "", "成品", "半成品", "原材料" };
        private List<string> _productAttributeOptions = new List<string> { "", "自制", "外购", "委外" };

        private ObservableCollection<PageButton> _pageButtons = new ObservableCollection<PageButton>();
        
        public ObservableCollection<ProductDisplayModel> Products
        {
            get => _products;
            set
            {
                _products = value;
                OnPropertyChanged(nameof(Products));
                OnPropertyChanged(nameof(IsEmpty));
            }
        }
        
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
            }
        }
        
        public string SearchKeyword
        {
            get => _searchKeyword;
            set
            {
                _searchKeyword = value;
                OnPropertyChanged(nameof(SearchKeyword));
            }
        }
        
        public ProductDisplayModel SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                _selectedProduct = value;
                OnPropertyChanged(nameof(SelectedProduct));
            }
        }
        
        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged(nameof(CurrentPage));
                LoadData();
            }
        }
        
        public int PageSize
        {
            get => _pageSize;
            set
            {
                _pageSize = value;
                OnPropertyChanged(nameof(PageSize));
            }
        }
        
        public int TotalCount
        {
            get => _totalCount;
            set
            {
                _totalCount = value;
                OnPropertyChanged(nameof(TotalCount));
            }
        }
        
        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged(nameof(TotalPages));
                UpdatePageButtons();
            }
        }
        
        public int GoToPage
        {
            get => _goToPage;
            set
            {
                _goToPage = value;
                OnPropertyChanged(nameof(GoToPage));
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                _errorMessage = value;
                OnPropertyChanged(nameof(ErrorMessage));
                OnPropertyChanged(nameof(HasError));
            }
        }

        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);

        public string SelectedUnit
        {
            get => _selectedUnit;
            set
            {
                _selectedUnit = value;
                OnPropertyChanged(nameof(SelectedUnit));
                // 自动触发搜索
                if (!_isInitializing)
                {
                    AutoSearch();
                }
            }
        }

        public string SelectedProductType
        {
            get => _selectedProductType;
            set
            {
                _selectedProductType = value;
                OnPropertyChanged(nameof(SelectedProductType));
                // 自动触发搜索
                if (!_isInitializing)
                {
                    AutoSearch();
                }
            }
        }

        public string SelectedProductAttribute
        {
            get => _selectedProductAttribute;
            set
            {
                _selectedProductAttribute = value;
                OnPropertyChanged(nameof(SelectedProductAttribute));
                // 自动触发搜索
                if (!_isInitializing)
                {
                    AutoSearch();
                }
            }
        }
        
        public List<string> UnitOptions => _unitOptions;
        public List<string> ProductTypeOptions => _productTypeOptions;
        public List<string> ProductAttributeOptions => _productAttributeOptions;
        
        public ObservableCollection<PageButton> PageButtons
        {
            get => _pageButtons;
            set
            {
                _pageButtons = value;
                OnPropertyChanged(nameof(PageButtons));
            }
        }
        
        public bool IsEmpty => Products?.Count == 0 && !IsLoading && !HasError;

        // 命令
        public ICommand SearchCommand { get; private set; }
        public ICommand ResetCommand { get; private set; }
        public ICommand ConfirmCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        public ICommand PrevPageCommand { get; private set; }
        public ICommand NextPageCommand { get; private set; }
        public ICommand GoToPageCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }

        // 关闭窗口的回调
        public Action<bool, ProductDisplayModel> CloseWindow { get; set; }

        // 初始化标志，防止在初始化时触发自动搜索
        private bool _isInitializing = true;

        public ProductSelectionViewModel()
        {
            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri("http://localhost:5005/");
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            // 添加认证头
            if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
            }

            InitializeCommands();
            LoadData();
            _isInitializing = false; // 初始化完成
        }
        
        private void InitializeCommands()
        {
            SearchCommand = new RelayCommand(Search);
            ResetCommand = new RelayCommand(Reset);
            ConfirmCommand = new RelayCommand(Confirm, CanConfirm);
            CancelCommand = new RelayCommand(Cancel);
            PrevPageCommand = new RelayCommand(PrevPage, CanPrevPage);
            NextPageCommand = new RelayCommand(NextPage, CanNextPage);
            GoToPageCommand = new RelayCommand(GoToPageExecute);
            RefreshCommand = new RelayCommand(Refresh);
        }
        
        private async void LoadData()
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            // 取消之前的搜索请求
            _searchCancellationTokenSource?.Cancel();
            _searchCancellationTokenSource = new CancellationTokenSource();

            IsLoading = true;
            ErrorMessage = "";
            Products.Clear();

            try
            {
                // 构建查询参数
                var url = BuildSearchUrl();

                System.Diagnostics.Debug.WriteLine($"Request URL: {url}");

                // 发起API请求
                var response = await _httpClient.GetAsync(url, _searchCancellationTokenSource.Token);
                var content = await response.Content.ReadAsStringAsync();

                System.Diagnostics.Debug.WriteLine($"API Response Status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        Converters = { new DateTimeConverter(), new NullableDateTimeConverter() }
                    };

                    var apiResponse = JsonSerializer.Deserialize<ApiResponse<PagedResult<ProductModel>>>(content, options);

                    if (apiResponse != null && apiResponse.Code == 200 && apiResponse.Result != null)
                    {
                        var result = apiResponse.Result;
                        TotalCount = result.Total;
                        TotalPages = result.TotalPages;

                        // 转换为显示模型
                        var displayModels = result.Items.Select((product, index) => new ProductDisplayModel
                        {
                            RowNumber = (CurrentPage - 1) * PageSize + index + 1,
                            Model = product
                        }).ToList();

                        Products = new ObservableCollection<ProductDisplayModel>(displayModels);

                        System.Diagnostics.Debug.WriteLine($"Loaded {Products.Count} products");

                        // 如果没有数据且不是第一页，自动跳转到第一页
                        if (Products.Count == 0 && CurrentPage > 1 && TotalCount > 0)
                        {
                            CurrentPage = 1;
                            return; // CurrentPage的setter会自动触发LoadData
                        }
                    }
                    else
                    {
                        ErrorMessage = apiResponse?.Message ?? "获取产品数据失败";
                        System.Diagnostics.Debug.WriteLine($"API response was not successful: {ErrorMessage}");
                    }
                }
                else
                {
                    ErrorMessage = $"请求失败: {response.StatusCode}";
                    System.Diagnostics.Debug.WriteLine($"API request failed: {response.StatusCode} - {content}");
                }
            }
            catch (OperationCanceledException)
            {
                // 请求被取消，不需要处理
                System.Diagnostics.Debug.WriteLine("Search request was cancelled");
            }
            catch (Exception ex)
            {
                ErrorMessage = $"加载数据时发生异常: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Exception: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
                OnPropertyChanged(nameof(IsEmpty));
            }
        }

        private string BuildSearchUrl()
        {
            var url = $"api/productionPlan/pageProduct?Page={CurrentPage}&PageSize={PageSize}";

            // 添加搜索条件
            if (!string.IsNullOrWhiteSpace(SearchKeyword))
            {
                url += $"&keyword={Uri.EscapeDataString(SearchKeyword.Trim())}";
            }
            if (!string.IsNullOrWhiteSpace(SelectedUnit))
            {
                url += $"&unit={Uri.EscapeDataString(SelectedUnit)}";
            }
            if (!string.IsNullOrWhiteSpace(SelectedProductType))
            {
                url += $"&productType={Uri.EscapeDataString(SelectedProductType)}";
            }
            if (!string.IsNullOrWhiteSpace(SelectedProductAttribute))
            {
                url += $"&productAttribute={Uri.EscapeDataString(SelectedProductAttribute)}";
            }

            return url;
        }
        
        private void UpdatePageButtons()
        {
            PageButtons.Clear();
            
            if (TotalPages <= 0) return;
            
            // 最多显示5个页码按钮
            int startPage = Math.Max(1, CurrentPage - 2);
            int endPage = Math.Min(TotalPages, startPage + 4);
            
            // 调整startPage，确保显示5个按钮（如果总页数足够）
            if (endPage - startPage < 4 && startPage > 1)
            {
                startPage = Math.Max(1, endPage - 4);
            }
            
            for (int i = startPage; i <= endPage; i++)
            {
                var pageButton = new PageButton();
                pageButton.PageNumber = i;
                pageButton.IsSelected = i == CurrentPage;
                PageButtons.Add(pageButton);
            }
        }
        
        private void Search()
        {
            CurrentPage = 1;
            LoadData();
        }

        private void Reset()
        {
            _isInitializing = true; // 防止重置时触发多次搜索
            SearchKeyword = "";
            SelectedUnit = "";
            SelectedProductType = "";
            SelectedProductAttribute = "";
            CurrentPage = 1;
            _isInitializing = false;
            LoadData();
        }

        private void Refresh()
        {
            LoadData();
        }

        // 自动搜索（延迟执行，避免频繁请求）
        private Timer _autoSearchTimer;
        private void AutoSearch()
        {
            _autoSearchTimer?.Dispose();
            _autoSearchTimer = new Timer(state =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    CurrentPage = 1;
                    LoadData();
                });
            }, null, 500, Timeout.Infinite); // 延迟500ms执行
        }
        
        private bool CanConfirm()
        {
            return SelectedProduct != null;
        }
        
        private void Confirm()
        {
            if (SelectedProduct != null)
            {
                System.Diagnostics.Debug.WriteLine($"确认选择产品: {SelectedProduct.ProductName} - {SelectedProduct.ProductCode}");
                CloseWindow?.Invoke(true, SelectedProduct);
            }
            else
            {
                MessageBox.Show("请选择一个产品", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private void Cancel()
        {
            CloseWindow?.Invoke(false, null);
        }
        
        private void PrevPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
            }
        }
        
        private bool CanPrevPage()
        {
            return CurrentPage > 1;
        }
        
        private void NextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
            }
        }
        
        private bool CanNextPage()
        {
            return CurrentPage < TotalPages;
        }
        
        private void GoToPageExecute()
        {
            if (int.TryParse(GoToPage.ToString(), out var page) && page > 0 && page <= TotalPages)
            {
                CurrentPage = page;
            }
            else if (TotalPages > 0)
            {
                // 输入无效时，重置为当前页
                GoToPage = CurrentPage;
            }
        }

        // 释放资源
        public void Dispose()
        {
            _searchCancellationTokenSource?.Cancel();
            _searchCancellationTokenSource?.Dispose();
            _autoSearchTimer?.Dispose();
            _httpClient?.Dispose();
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
    
    // 用于显示在DataGrid中的模型
    public class ProductDisplayModel
    {
        public int RowNumber { get; set; }
        public ProductModel Model { get; set; }
        
        public string Id => Model?.Id;
        public string ProductCode => Model?.ProductCode;
        public string ProductName => Model?.ProductName;
        public string Specification => Model?.Specification;
        public string Unit => Model?.Unit;
        public string ProductType => Model?.ProductType;
        public string ProductAttribute => Model?.ProductAttribute;
        
        // 添加调试信息
        public override string ToString()
        {
            return $"ProductDisplayModel: Name={ProductName}, Code={ProductCode}, Spec={Specification}, Unit={Unit}, Type={ProductType}";
        }
    }
}
