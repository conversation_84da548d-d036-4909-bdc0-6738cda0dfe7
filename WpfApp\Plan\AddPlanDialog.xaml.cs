using System.Windows.Controls;

namespace WpfApp.Plan
{
    /// <summary>
    /// AddPlanDialog.xaml 的交互逻辑
    /// </summary>
    public partial class AddPlanDialog : UserControl
    {
        public AddPlanDialog()
        {
            InitializeComponent();
            // 不要设置 DataContext，让它继承父级
        }

        private void UploadAttachment_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("=== UploadAttachment_Click 被触发 ===");
            System.Windows.MessageBox.Show("按钮点击事件被触发", "调试", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

            // 调用PlanViewModel的UploadAttachment方法
            if (this.DataContext is PlanViewModel vm)
            {
                System.Diagnostics.Debug.WriteLine("DataContext 是 PlanViewModel");
                System.Diagnostics.Debug.WriteLine($"UploadAttachmentCommand 是否为空: {vm.UploadAttachmentCommand == null}");
                vm.UploadAttachmentCommand?.Execute(null);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"DataContext 不是 PlanViewModel，实际类型: {this.DataContext?.GetType().Name ?? "null"}");
                System.Windows.MessageBox.Show($"DataContext 类型错误: {this.DataContext?.GetType().Name ?? "null"}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }
    }
} 