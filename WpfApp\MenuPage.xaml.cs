﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using WpfApp.Plan;

namespace WpfApp
{
    public partial class MenuPage : UserControl
    {
        public MenuPage()
        {
            InitializeComponent();
            var vm = new MenuPageViewModel();
            vm.MenuChanged += OnMenuChanged;
            this.DataContext = vm;
            // 默认显示首页
            MenuContent.Content = new TextBlock { Text = "首页", FontSize = 32, VerticalAlignment = System.Windows.VerticalAlignment.Center, HorizontalAlignment = System.Windows.HorizontalAlignment.Center };
        }

        // 处理生产计划新增请求
        public void OnAddPlanRequested()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始处理新增计划请求 ===");
                
                // 创建编辑计划页面和ViewModel
                var editPlanViewModel = new EditPlanViewModel(isNewPlan: true, backAction: () =>
                {
                    // 返回到生产计划页面
                    OnMenuChanged("生产计划");
                });
                
                var editPlanPage = new EditPlanPage();
                editPlanPage.DataContext = editPlanViewModel;

                // 显示编辑计划页面
                MenuContent.Content = editPlanPage;
                
                System.Diagnostics.Debug.WriteLine("=== 新增计划页面已显示 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理新增计划请求时发生异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"打开新增计划页面时出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnMenuChanged(string menu)
        {
            switch (menu)
            {
                case "BOM":
                    MenuContent.Content = new BomPage { DataContext = new BomViewModel() };
                    break;
                case "首页":
                    MenuContent.Content = new TextBlock { Text = "首页内容", FontSize = 32, VerticalAlignment = VerticalAlignment.Center, HorizontalAlignment = HorizontalAlignment.Center };
                    break;
                case "生产计划":
                    var planPage = new PlanPage();
                    var planViewModel = new PlanViewModel();
                    // 订阅新增计划请求
                    planViewModel.AddPlanRequested += OnAddPlanRequested;
                    planPage.DataContext = planViewModel;
                    MenuContent.Content = planPage;
                    break;
                // 可以继续添加其他有具体页面的菜单case
                default:
                    // 没有对应页面的菜单显示菜单名称
                    MenuContent.Content = new TextBlock { 
                        Text = menu, 
                        FontSize = 32, 
                        VerticalAlignment = VerticalAlignment.Center, 
                        HorizontalAlignment = HorizontalAlignment.Center 
                    };
                    break;
            }
        }
    }
}
